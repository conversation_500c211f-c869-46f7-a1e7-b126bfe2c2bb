<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_profileview" modulePackage="Manaknight" filePath="app\src\main\res\layout\fragment_profileview.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout" rootNodeViewId="@+id/constraint"><Targets><Target id="@+id/constraint" tag="layout/fragment_profileview_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="283" endOffset="51"/></Target><Target id="@+id/headerInclude" tag="layout/fragment_profileview_0" include="header"><Expressions/><location startLine="10" startOffset="4" endLine="14" endOffset="18"/></Target><Target id="@+id/scrollable" view="ScrollView"><Expressions/><location startLine="16" startOffset="4" endLine="275" endOffset="16"/></Target><Target id="@+id/line1" view="LinearLayout"><Expressions/><location startLine="27" startOffset="8" endLine="273" endOffset="22"/></Target><Target id="@+id/btnEdit" view="ImageView"><Expressions/><location startLine="60" startOffset="20" endLine="67" endOffset="54"/></Target><Target id="@+id/txtFName" view="TextView"><Expressions/><location startLine="88" startOffset="20" endLine="99" endOffset="52"/></Target><Target id="@+id/txtLName" view="TextView"><Expressions/><location startLine="125" startOffset="20" endLine="136" endOffset="52"/></Target><Target id="@+id/txtCompanyName" view="TextView"><Expressions/><location startLine="162" startOffset="20" endLine="173" endOffset="52"/></Target><Target id="@+id/txtEmail" view="TextView"><Expressions/><location startLine="199" startOffset="20" endLine="210" endOffset="52"/></Target><Target id="@+id/btnContinue" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="248" startOffset="16" endLine="264" endOffset="93"/></Target></Targets></Layout>