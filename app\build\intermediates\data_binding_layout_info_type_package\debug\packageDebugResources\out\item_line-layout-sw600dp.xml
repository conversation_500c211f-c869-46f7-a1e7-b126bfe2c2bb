<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_line" modulePackage="Manaknight" filePath="app\src\main\res\layout-sw600dp\item_line.xml" directory="layout-sw600dp" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout-sw600dp/item_line_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="2" startOffset="4" endLine="224" endOffset="55"/></Target><Target id="@+id/description" view="TextView"><Expressions/><location startLine="26" startOffset="12" endLine="35" endOffset="44"/></Target><Target id="@+id/btnDelete" view="ImageView"><Expressions/><location startLine="37" startOffset="12" endLine="44" endOffset="48"/></Target><Target id="@+id/btnEdit" view="ImageView"><Expressions/><location startLine="46" startOffset="12" endLine="53" endOffset="46"/></Target><Target id="@+id/txtSalePrice" view="TextView"><Expressions/><location startLine="64" startOffset="12" endLine="73" endOffset="44"/></Target><Target id="@+id/txtSalePrice_1" view="TextView"><Expressions/><location startLine="75" startOffset="12" endLine="87" endOffset="44"/></Target><Target id="@+id/txtLaboutBudget" view="TextView"><Expressions/><location startLine="96" startOffset="12" endLine="105" endOffset="44"/></Target><Target id="@+id/txtLaboutBudget_1" view="TextView"><Expressions/><location startLine="107" startOffset="12" endLine="119" endOffset="44"/></Target><Target id="@+id/txtProfitOverhead" view="TextView"><Expressions/><location startLine="128" startOffset="12" endLine="137" endOffset="44"/></Target><Target id="@+id/txtProfitOverhead_1" view="TextView"><Expressions/><location startLine="139" startOffset="12" endLine="151" endOffset="44"/></Target><Target id="@+id/txtMaterialBudget" view="TextView"><Expressions/><location startLine="160" startOffset="12" endLine="169" endOffset="44"/></Target><Target id="@+id/txtMaterialBudget_1" view="TextView"><Expressions/><location startLine="171" startOffset="12" endLine="183" endOffset="44"/></Target><Target id="@+id/txtTypeLabel" view="TextView"><Expressions/><location startLine="202" startOffset="12" endLine="211" endOffset="44"/></Target></Targets></Layout>