package com.manaknight.app.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u008a\u0001\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\u001a\u0010\u0010\u0006\u001a\u00020\u00072\u0006\u0010\u0000\u001a\u00020\u0001H\u0007\u001a\u0010\u0010\b\u001a\u00020\u00072\u0006\u0010\t\u001a\u00020\nH\u0007\u001a2\u0010\u000b\u001a\u00020\u00072\b\u0010\f\u001a\u0004\u0018\u00010\r2\b\u0010\u000e\u001a\u0004\u0018\u00010\r2\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\r2\b\b\u0002\u0010\u0010\u001a\u00020\u0001H\u0007\u001a\u0016\u0010\u0011\u001a\u00020\u00072\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00070\u0013H\u0007\u001a,\u0010\u0014\u001a\u00020\u00072\u0006\u0010\u0015\u001a\u00020\u00162\b\u0010\u0017\u001a\u0004\u0018\u00010\u00162\u0006\u0010\u0018\u001a\u00020\u00192\b\b\u0002\u0010\u001a\u001a\u00020\u001bH\u0007\u001a\u0010\u0010\u001c\u001a\u00020\u00072\u0006\u0010\u001d\u001a\u00020\u001eH\u0007\u001a \u0010\u001f\u001a\u00020\u00072\u0006\u0010\u001d\u001a\u00020\u001e2\u000e\u0010 \u001a\n\u0012\u0004\u0012\u00020\u001e\u0018\u00010!H\u0007\u001a\u0016\u0010\"\u001a\u00020\u00072\f\u0010#\u001a\b\u0012\u0004\u0012\u00020$0!H\u0007\u001a\b\u0010%\u001a\u00020\u0007H\u0007\u001aL\u0010&\u001a\u00020\u00072\u0006\u0010\'\u001a\u00020(2\u0012\u0010\u0012\u001a\u000e\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\u00070)2\u0006\u0010*\u001a\u00020+2\b\u0010,\u001a\u0004\u0018\u00010(2\u0006\u0010-\u001a\u00020.2\f\u0010/\u001a\b\u0012\u0004\u0012\u00020\u00070\u0013H\u0007\u001aJ\u00100\u001a\u00020\u00072\u0006\u00101\u001a\u00020\u00192\u0006\u0010-\u001a\u00020.2\u0012\u0010\u0012\u001a\u000e\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\u00070)2\u0006\u0010*\u001a\u00020+2\u0006\u00102\u001a\u0002032\f\u0010/\u001a\b\u0012\u0004\u0012\u00020\u00070\u0013H\u0007\u001a\u0010\u00104\u001a\u00020\u00072\u0006\u00105\u001a\u000206H\u0007\u001a\u001e\u00107\u001a\u00020\u00072\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00070\u00132\u0006\u00108\u001a\u00020\u0019H\u0007\u001a*\u00109\u001a\b\u0012\u0004\u0012\u00020:0!2\f\u0010;\u001a\b\u0012\u0004\u0012\u00020\u00160!2\u000e\u0010<\u001a\n\u0012\u0004\u0012\u00020\u0016\u0018\u00010!\"\u001a\u0010\u0000\u001a\u00020\u0001X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0002\u0010\u0003\"\u0004\b\u0004\u0010\u0005\u00a8\u0006="}, d2 = {"dontShow", "", "getDontShow", "()Z", "setDontShow", "(Z)V", "CheckboxWithLabel", "", "ClientDetailsCard", "clientDetails", "Lcom/manaknight/app/model/remote/profitPro/ClientDetailRespModel;", "DetailRow", "label", "", "value", "previousValue", "hasChanged", "EditButton", "onClick", "Lkotlin/Function0;", "JobDetailCard", "jobDetail", "Lcom/manaknight/app/model/remote/profitPro/JobDetailsRespModel;", "previousJobDetail", "index", "", "changeStatus", "Lcom/manaknight/app/ui/screens/ChangeStatus;", "MaterialDetailRow", "material", "Lcom/manaknight/app/model/remote/profitPro/MaterialRespModel;", "MaterialItem", "previousMaterials", "", "PaymentStagesCard", "details", "Lcom/manaknight/app/model/remote/profitPro/DrawsRespModel;", "PreviewProjectDetailScreen", "ProjectDetailContent", "apiResponse", "Lcom/manaknight/app/model/remote/profitPro/AllLineItemsResponseModel;", "Lkotlin/Function1;", "navController", "Landroidx/navigation/NavController;", "cachedProjectDetailsResource", "baasViewModel", "Lcom/manaknight/app/viewmodels/BaasViewModel;", "onNavigateBack", "ProjectDetailMainScreen", "projectId", "dialog", "Landroid/app/Dialog;", "ProjectTotalsCard", "totals", "Lcom/manaknight/app/model/remote/profitPro/TotalRespModel;", "SendEstimateButton", "status", "evaluateProjectChanges", "Lcom/manaknight/app/ui/screens/JobDetailsUIModel;", "newItems", "oldItems", "app_debug"})
public final class PreviewProjectDetailScreenKt {
    private static boolean dontShow = false;
    
    public static final boolean getDontShow() {
        return false;
    }
    
    public static final void setDontShow(boolean p0) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ProjectDetailMainScreen(int projectId, @org.jetbrains.annotations.NotNull()
    com.manaknight.app.viewmodels.BaasViewModel baasViewModel, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onClick, @org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    android.app.Dialog dialog, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ProjectDetailContent(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.AllLineItemsResponseModel apiResponse, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onClick, @org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.Nullable()
    com.manaknight.app.model.remote.profitPro.AllLineItemsResponseModel cachedProjectDetailsResource, @org.jetbrains.annotations.NotNull()
    com.manaknight.app.viewmodels.BaasViewModel baasViewModel, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void CheckboxWithLabel(boolean dontShow) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void SendEstimateButton(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick, int status) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void EditButton(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void JobDetailCard(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.JobDetailsRespModel jobDetail, @org.jetbrains.annotations.Nullable()
    com.manaknight.app.model.remote.profitPro.JobDetailsRespModel previousJobDetail, int index, @org.jetbrains.annotations.NotNull()
    com.manaknight.app.ui.screens.ChangeStatus changeStatus) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void MaterialDetailRow(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.MaterialRespModel material) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void MaterialItem(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.MaterialRespModel material, @org.jetbrains.annotations.Nullable()
    java.util.List<com.manaknight.app.model.remote.profitPro.MaterialRespModel> previousMaterials) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void DetailRow(@org.jetbrains.annotations.Nullable()
    java.lang.String label, @org.jetbrains.annotations.Nullable()
    java.lang.String value, @org.jetbrains.annotations.Nullable()
    java.lang.String previousValue, boolean hasChanged) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ProjectTotalsCard(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.TotalRespModel totals) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void PaymentStagesCard(@org.jetbrains.annotations.NotNull()
    java.util.List<com.manaknight.app.model.remote.profitPro.DrawsRespModel> details) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ClientDetailsCard(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.ClientDetailRespModel clientDetails) {
    }
    
    @androidx.compose.ui.tooling.preview.Preview(showBackground = true, widthDp = 800, heightDp = 1200, name = "Tablet Layout")
    @androidx.compose.runtime.Composable()
    public static final void PreviewProjectDetailScreen() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.util.List<com.manaknight.app.ui.screens.JobDetailsUIModel> evaluateProjectChanges(@org.jetbrains.annotations.NotNull()
    java.util.List<com.manaknight.app.model.remote.profitPro.JobDetailsRespModel> newItems, @org.jetbrains.annotations.Nullable()
    java.util.List<com.manaknight.app.model.remote.profitPro.JobDetailsRespModel> oldItems) {
        return null;
    }
}