<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_broadcast_video" modulePackage="Manaknight" filePath="app\src\main\res\layout\item_broadcast_video.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/item_broadcast_video_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="4" endLine="69" endOffset="55"/></Target><Target id="@+id/cvImage" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="9" startOffset="8" endLine="25" endOffset="43"/></Target><Target id="@+id/imageView" view="ImageView"><Expressions/><location startLine="17" startOffset="12" endLine="23" endOffset="51"/></Target><Target id="@+id/tvName" view="TextView"><Expressions/><location startLine="27" startOffset="8" endLine="36" endOffset="55"/></Target><Target id="@+id/tvCountry" view="TextView"><Expressions/><location startLine="38" startOffset="8" endLine="47" endOffset="63"/></Target><Target id="@+id/tvUploadTime" view="TextView"><Expressions/><location startLine="49" startOffset="8" endLine="58" endOffset="66"/></Target><Target id="@+id/tvPrice" view="TextView"><Expressions/><location startLine="60" startOffset="8" endLine="67" endOffset="67"/></Target></Targets></Layout>