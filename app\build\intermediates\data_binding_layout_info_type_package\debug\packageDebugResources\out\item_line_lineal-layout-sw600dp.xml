<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_line_lineal" modulePackage="Manaknight" filePath="app\src\main\res\layout-sw600dp\item_line_lineal.xml" directory="layout-sw600dp" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout-sw600dp/item_line_lineal_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="98" endOffset="35"/></Target><Target id="@+id/radioButtonMaterial" view="RadioButton"><Expressions/><location startLine="20" startOffset="8" endLine="26" endOffset="54"/></Target><Target id="@+id/txtLinealItemName" view="TextView"><Expressions/><location startLine="29" startOffset="8" endLine="42" endOffset="54"/></Target><Target id="@+id/txtUnitStart" view="TextView"><Expressions/><location startLine="45" startOffset="8" endLine="56" endOffset="72"/></Target><Target id="@+id/txtUnit" view="TextView"><Expressions/><location startLine="58" startOffset="8" endLine="70" endOffset="72"/></Target><Target id="@+id/edtUnits" view="EditText"><Expressions/><location startLine="73" startOffset="8" endLine="88" endOffset="54"/></Target></Targets></Layout>