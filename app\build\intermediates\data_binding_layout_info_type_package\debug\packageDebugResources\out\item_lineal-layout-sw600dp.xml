<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_lineal" modulePackage="Manaknight" filePath="app\src\main\res\layout-sw600dp\item_lineal.xml" directory="layout-sw600dp" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout-sw600dp/item_lineal_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="2" startOffset="4" endLine="251" endOffset="55"/></Target><Target id="@+id/name" view="TextView"><Expressions/><location startLine="25" startOffset="12" endLine="34" endOffset="44"/></Target><Target id="@+id/btnDelete" view="ImageView"><Expressions/><location startLine="36" startOffset="12" endLine="43" endOffset="48"/></Target><Target id="@+id/btnEdit" view="ImageView"><Expressions/><location startLine="45" startOffset="12" endLine="52" endOffset="46"/></Target><Target id="@+id/txtMName" view="TextView"><Expressions/><location startLine="73" startOffset="12" endLine="85" endOffset="44"/></Target><Target id="@+id/txtLinealCost" view="TextView"><Expressions/><location startLine="104" startOffset="12" endLine="116" endOffset="44"/></Target><Target id="@+id/txtProfitOverhead" view="TextView"><Expressions/><location startLine="143" startOffset="16" endLine="152" endOffset="48"/></Target><Target id="@+id/txtRemaing" view="TextView"><Expressions/><location startLine="167" startOffset="12" endLine="177" endOffset="44"/></Target><Target id="@+id/txtLabourCost" view="TextView"><Expressions/><location startLine="197" startOffset="12" endLine="209" endOffset="44"/></Target><Target id="@+id/txtMaterialCost" view="TextView"><Expressions/><location startLine="228" startOffset="12" endLine="240" endOffset="44"/></Target></Targets></Layout>