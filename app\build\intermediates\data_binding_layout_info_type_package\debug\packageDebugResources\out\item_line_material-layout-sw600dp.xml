<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_line_material" modulePackage="Manaknight" filePath="app\src\main\res\layout-sw600dp\item_line_material.xml" directory="layout-sw600dp" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout-sw600dp/item_line_material_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="124" endOffset="35"/></Target><Target id="@+id/checkBoxMaterial" view="CheckBox"><Expressions/><location startLine="19" startOffset="8" endLine="25" endOffset="54"/></Target><Target id="@+id/txtMaterialName" view="TextView"><Expressions/><location startLine="28" startOffset="8" endLine="41" endOffset="54"/></Target><Target id="@+id/txtPrice" view="TextView"><Expressions/><location startLine="43" startOffset="8" endLine="54" endOffset="70"/></Target><Target id="@+id/txtTotalPrice" view="TextView"><Expressions/><location startLine="56" startOffset="8" endLine="67" endOffset="70"/></Target><Target id="@+id/txtUnitStart" view="TextView"><Expressions/><location startLine="70" startOffset="8" endLine="81" endOffset="63"/></Target><Target id="@+id/txtUnit" view="TextView"><Expressions/><location startLine="83" startOffset="8" endLine="95" endOffset="63"/></Target><Target id="@+id/edtUnits" view="EditText"><Expressions/><location startLine="98" startOffset="8" endLine="114" endOffset="54"/></Target></Targets></Layout>