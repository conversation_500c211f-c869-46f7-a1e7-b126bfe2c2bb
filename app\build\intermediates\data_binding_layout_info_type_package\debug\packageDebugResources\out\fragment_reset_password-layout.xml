<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_reset_password" modulePackage="Manaknight" filePath="app\src\main\res\layout\fragment_reset_password.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout" rootNodeViewId="@+id/constraint"><Targets><Target id="@+id/constraint" tag="layout/fragment_reset_password_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="134" endOffset="51"/></Target><Target id="@+id/headerInclude" tag="layout/fragment_reset_password_0" include="header"><Expressions/><location startLine="11" startOffset="4" endLine="15" endOffset="18"/></Target><Target id="@+id/tvCode" view="TextView"><Expressions/><location startLine="17" startOffset="4" endLine="29" endOffset="66"/></Target><Target id="@+id/edTxtCode" view="EditText"><Expressions/><location startLine="31" startOffset="4" endLine="47" endOffset="9"/></Target><Target id="@+id/tvPassword" view="TextView"><Expressions/><location startLine="49" startOffset="4" endLine="61" endOffset="62"/></Target><Target id="@+id/edTxtPassword" view="EditText"><Expressions/><location startLine="63" startOffset="4" endLine="79" endOffset="9"/></Target><Target id="@+id/tvPasswordConfirm" view="TextView"><Expressions/><location startLine="82" startOffset="4" endLine="94" endOffset="66"/></Target><Target id="@+id/edTxtPasswordConfirm" view="EditText"><Expressions/><location startLine="96" startOffset="4" endLine="112" endOffset="9"/></Target><Target id="@+id/btnResetPassword" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="115" startOffset="4" endLine="132" endOffset="73"/></Target></Targets></Layout>