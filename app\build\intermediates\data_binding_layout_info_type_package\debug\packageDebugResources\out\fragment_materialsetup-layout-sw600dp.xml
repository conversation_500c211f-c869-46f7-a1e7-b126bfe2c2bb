<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_materialsetup" modulePackage="Manaknight" filePath="app\src\main\res\layout-sw600dp\fragment_materialsetup.xml" directory="layout-sw600dp" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout-sw600dp/fragment_materialsetup_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="101" endOffset="51"/></Target><Target id="@+id/headerInclude" tag="layout-sw600dp/fragment_materialsetup_0" include="header"><Expressions/><location startLine="9" startOffset="4" endLine="13" endOffset="45"/></Target><Target id="@+id/container" view="RelativeLayout"><Expressions/><location startLine="15" startOffset="4" endLine="96" endOffset="20"/></Target><Target id="@+id/innerConstraintLayout" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="25" startOffset="8" endLine="95" endOffset="59"/></Target><Target id="@+id/materialRecylerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="44" startOffset="20" endLine="50" endOffset="65"/></Target><Target id="@+id/btnAddMaterial" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="53" startOffset="20" endLine="72" endOffset="47"/></Target><Target id="@+id/btnContinue" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="74" startOffset="20" endLine="91" endOffset="97"/></Target></Targets></Layout>