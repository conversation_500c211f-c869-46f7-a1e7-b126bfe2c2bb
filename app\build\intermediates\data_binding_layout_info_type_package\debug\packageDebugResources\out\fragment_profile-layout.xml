<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_profile" modulePackage="Manaknight" filePath="app\src\main\res\layout\fragment_profile.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_profile_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="4" endLine="121" endOffset="51"/></Target><Target id="@+id/materialCardView2" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="25" startOffset="12" endLine="42" endOffset="47"/></Target><Target id="@+id/ivUser" view="ImageView"><Expressions/><location startLine="35" startOffset="16" endLine="40" endOffset="69"/></Target><Target id="@+id/ivEdit" view="ImageView"><Expressions/><location startLine="44" startOffset="12" endLine="51" endOffset="75"/></Target><Target id="@+id/textViewFirstName" view="TextView"><Expressions/><location startLine="52" startOffset="12" endLine="61" endOffset="66"/></Target><Target id="@+id/textViewLastName" view="TextView"><Expressions/><location startLine="63" startOffset="12" endLine="72" endOffset="77"/></Target><Target id="@+id/textViewEmail" view="TextView"><Expressions/><location startLine="73" startOffset="12" endLine="82" endOffset="76"/></Target><Target id="@+id/buttonEditProfile" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="85" startOffset="12" endLine="97" endOffset="39"/></Target><Target id="@+id/btnLogout" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="99" startOffset="12" endLine="113" endOffset="39"/></Target></Targets></Layout>