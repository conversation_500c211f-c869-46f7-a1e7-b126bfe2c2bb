<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="header" modulePackage="Manaknight" filePath="app\src\main\res\layout\header.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/header_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="86" endOffset="51"/></Target><Target id="@+id/header" view="LinearLayout"><Expressions/><location startLine="8" startOffset="4" endLine="83" endOffset="18"/></Target><Target id="@+id/add_plater_title" view="TextView"><Expressions/><location startLine="31" startOffset="12" endLine="44" endOffset="42"/></Target><Target id="@+id/backButton" view="ImageView"><Expressions/><location startLine="47" startOffset="8" endLine="53" endOffset="41"/></Target><Target id="@+id/btnSave" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="58" startOffset="8" endLine="75" endOffset="43"/></Target></Targets></Layout>