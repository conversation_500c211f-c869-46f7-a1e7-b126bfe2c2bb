<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_material" modulePackage="Manaknight" filePath="app\src\main\res\layout\item_material.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/item_material_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="2" startOffset="4" endLine="130" endOffset="55"/></Target><Target id="@+id/name" view="TextView"><Expressions/><location startLine="26" startOffset="12" endLine="36" endOffset="44"/></Target><Target id="@+id/btnDelete" view="ImageView"><Expressions/><location startLine="38" startOffset="12" endLine="45" endOffset="48"/></Target><Target id="@+id/btnEdit" view="ImageView"><Expressions/><location startLine="47" startOffset="12" endLine="54" endOffset="46"/></Target><Target id="@+id/txtMName" view="TextView"><Expressions/><location startLine="75" startOffset="12" endLine="87" endOffset="44"/></Target><Target id="@+id/txtUnitCost" view="TextView"><Expressions/><location startLine="107" startOffset="12" endLine="119" endOffset="44"/></Target></Targets></Layout>