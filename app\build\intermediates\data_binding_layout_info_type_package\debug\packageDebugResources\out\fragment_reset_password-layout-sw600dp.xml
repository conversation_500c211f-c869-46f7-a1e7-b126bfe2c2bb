<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_reset_password" modulePackage="Manaknight" filePath="app\src\main\res\layout-sw600dp\fragment_reset_password.xml" directory="layout-sw600dp" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout" rootNodeViewId="@+id/constraint"><Targets><Target id="@+id/constraint" tag="layout-sw600dp/fragment_reset_password_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="145" endOffset="51"/></Target><Target id="@+id/headerInclude" tag="layout-sw600dp/fragment_reset_password_0" include="header"><Expressions/><location startLine="11" startOffset="4" endLine="15" endOffset="45"/></Target><Target id="@+id/container" view="RelativeLayout"><Expressions/><location startLine="17" startOffset="4" endLine="141" endOffset="20"/></Target><Target id="@+id/innerConstraintLayout" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="27" startOffset="8" endLine="140" endOffset="59"/></Target><Target id="@+id/tvCode" view="TextView"><Expressions/><location startLine="34" startOffset="8" endLine="45" endOffset="55"/></Target><Target id="@+id/edTxtCode" view="EditText"><Expressions/><location startLine="47" startOffset="8" endLine="61" endOffset="63"/></Target><Target id="@+id/tvPassword" view="TextView"><Expressions/><location startLine="63" startOffset="8" endLine="74" endOffset="66"/></Target><Target id="@+id/edTxtPassword" view="EditText"><Expressions/><location startLine="76" startOffset="8" endLine="90" endOffset="67"/></Target><Target id="@+id/tvPasswordConfirm" view="TextView"><Expressions/><location startLine="93" startOffset="8" endLine="104" endOffset="70"/></Target><Target id="@+id/edTxtPasswordConfirm" view="EditText"><Expressions/><location startLine="106" startOffset="8" endLine="120" endOffset="74"/></Target><Target id="@+id/btnResetPassword" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="123" startOffset="8" endLine="139" endOffset="77"/></Target></Targets></Layout>