<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_profileview" modulePackage="Manaknight" filePath="app\src\main\res\layout-sw600dp\fragment_profileview.xml" directory="layout-sw600dp" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout" rootNodeViewId="@+id/constraint"><Targets><Target id="@+id/constraint" tag="layout-sw600dp/fragment_profileview_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="293" endOffset="51"/></Target><Target id="@+id/headerInclude" tag="layout-sw600dp/fragment_profileview_0" include="header"><Expressions/><location startLine="10" startOffset="4" endLine="14" endOffset="45"/></Target><Target id="@+id/container" view="RelativeLayout"><Expressions/><location startLine="16" startOffset="4" endLine="287" endOffset="20"/></Target><Target id="@+id/innerConstraintLayout" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="26" startOffset="8" endLine="286" endOffset="59"/></Target><Target id="@+id/scrollable" view="ScrollView"><Expressions/><location startLine="32" startOffset="12" endLine="285" endOffset="24"/></Target><Target id="@+id/line1" view="LinearLayout"><Expressions/><location startLine="42" startOffset="16" endLine="283" endOffset="30"/></Target><Target id="@+id/btnEdit" view="ImageView"><Expressions/><location startLine="74" startOffset="28" endLine="81" endOffset="62"/></Target><Target id="@+id/txtFName" view="TextView"><Expressions/><location startLine="102" startOffset="28" endLine="113" endOffset="57"/></Target><Target id="@+id/txtLName" view="TextView"><Expressions/><location startLine="139" startOffset="28" endLine="150" endOffset="57"/></Target><Target id="@+id/txtCompanyName" view="TextView"><Expressions/><location startLine="176" startOffset="28" endLine="187" endOffset="57"/></Target><Target id="@+id/txtEmail" view="TextView"><Expressions/><location startLine="213" startOffset="28" endLine="224" endOffset="57"/></Target><Target id="@+id/btnContinue" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="261" startOffset="24" endLine="277" endOffset="101"/></Target></Targets></Layout>