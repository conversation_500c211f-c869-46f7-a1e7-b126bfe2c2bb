<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_subscription" modulePackage="Manaknight" filePath="app\src\main\res\layout\fragment_subscription.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_subscription_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="3" endLine="264" endOffset="51"/></Target><Target id="@+id/headerInclude" tag="layout/fragment_subscription_0" include="header"><Expressions/><location startLine="10" startOffset="4" endLine="14" endOffset="18"/></Target><Target id="@+id/dialogCardView" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="17" startOffset="4" endLine="243" endOffset="39"/></Target><Target id="@+id/tvPrice" view="TextView"><Expressions/><location startLine="58" startOffset="16" endLine="67" endOffset="49"/></Target><Target id="@+id/btnTrail" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="245" startOffset="4" endLine="262" endOffset="67"/></Target></Targets></Layout>