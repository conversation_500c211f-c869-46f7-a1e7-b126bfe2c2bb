<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_line_total" modulePackage="Manaknight" filePath="app\src\main\res\layout-sw600dp\item_line_total.xml" directory="layout-sw600dp" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout-sw600dp/item_line_total_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="2" startOffset="4" endLine="203" endOffset="55"/></Target><Target id="@+id/txtSalePrice" view="TextView"><Expressions/><location startLine="25" startOffset="12" endLine="34" endOffset="44"/></Target><Target id="@+id/txtSalePrice_1" view="TextView"><Expressions/><location startLine="36" startOffset="12" endLine="48" endOffset="44"/></Target><Target id="@+id/txtProfitOverhead" view="TextView"><Expressions/><location startLine="57" startOffset="12" endLine="66" endOffset="44"/></Target><Target id="@+id/txtProfitOverhead_1" view="TextView"><Expressions/><location startLine="68" startOffset="12" endLine="80" endOffset="44"/></Target><Target id="@+id/txtMaterialBudget" view="TextView"><Expressions/><location startLine="89" startOffset="12" endLine="98" endOffset="44"/></Target><Target id="@+id/txtMaterialBudget_1" view="TextView"><Expressions/><location startLine="100" startOffset="12" endLine="112" endOffset="44"/></Target><Target id="@+id/txtLaboutBudget" view="TextView"><Expressions/><location startLine="122" startOffset="12" endLine="131" endOffset="44"/></Target><Target id="@+id/txtLaboutBudget_1" view="TextView"><Expressions/><location startLine="133" startOffset="12" endLine="145" endOffset="44"/></Target><Target id="@+id/btnSave" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="159" startOffset="12" endLine="173" endOffset="46"/></Target><Target id="@+id/btnContinue" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="176" startOffset="12" endLine="190" endOffset="44"/></Target></Targets></Layout>