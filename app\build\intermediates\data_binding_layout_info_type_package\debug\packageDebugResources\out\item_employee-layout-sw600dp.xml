<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_employee" modulePackage="Manaknight" filePath="app\src\main\res\layout-sw600dp\item_employee.xml" directory="layout-sw600dp" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout-sw600dp/item_employee_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="2" startOffset="4" endLine="127" endOffset="55"/></Target><Target id="@+id/name" view="TextView"><Expressions/><location startLine="26" startOffset="12" endLine="35" endOffset="44"/></Target><Target id="@+id/btnEdit" view="ImageView"><Expressions/><location startLine="37" startOffset="12" endLine="44" endOffset="46"/></Target><Target id="@+id/btnDelete" view="ImageView"><Expressions/><location startLine="46" startOffset="12" endLine="53" endOffset="48"/></Target><Target id="@+id/txtFName" view="TextView"><Expressions/><location startLine="74" startOffset="12" endLine="85" endOffset="44"/></Target><Target id="@+id/txtRate" view="TextView"><Expressions/><location startLine="105" startOffset="12" endLine="116" endOffset="44"/></Target></Targets></Layout>