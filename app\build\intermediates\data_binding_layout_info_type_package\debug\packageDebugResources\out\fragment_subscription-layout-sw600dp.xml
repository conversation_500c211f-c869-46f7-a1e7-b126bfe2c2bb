<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_subscription" modulePackage="Manaknight" filePath="app\src\main\res\layout-sw600dp\fragment_subscription.xml" directory="layout-sw600dp" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout-sw600dp/fragment_subscription_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="282" endOffset="51"/></Target><Target id="@+id/headerInclude" tag="layout-sw600dp/fragment_subscription_0" include="header"><Expressions/><location startLine="10" startOffset="4" endLine="14" endOffset="45"/></Target><Target id="@+id/container" view="RelativeLayout"><Expressions/><location startLine="16" startOffset="4" endLine="277" endOffset="20"/></Target><Target id="@+id/innerConstraintLayout" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="26" startOffset="8" endLine="276" endOffset="59"/></Target><Target id="@+id/dialogCardView" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="32" startOffset="12" endLine="256" endOffset="47"/></Target><Target id="@+id/tvPrice" view="TextView"><Expressions/><location startLine="73" startOffset="24" endLine="82" endOffset="53"/></Target><Target id="@+id/btnTrail" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="258" startOffset="12" endLine="274" endOffset="75"/></Target></Targets></Layout>